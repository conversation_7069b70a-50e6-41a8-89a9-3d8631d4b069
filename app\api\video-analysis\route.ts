import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { SubtitleExtractor, SubtitleExtractionResult } from '@/utils/subtitle-extractor';

const execAsync = promisify(exec);

// 视频分析结果接口
interface VideoAnalysisResult {
  success: boolean;
  data?: {
    videoInfo: {
      title: string;
      duration: number;
      description?: string;
    };
    audioAnalysis: {
      transcript: string;
      segments: Array<{
        start: number;
        end: number;
        text: string;
      }>;
      summary: string;
    };
    subtitleAnalysis: {
      hasSubtitles: boolean;
      language: string;
      content: string;
      segments: Array<{
        start: number;
        end: number;
        text: string;
        index: number;
      }>;
      confidence: number;
    };
    combinedSummary: string;
    structuredContent: string;
  };
  error?: string;
  progress?: number;
}

// 临时文件目录
const TEMP_DIR = path.join(process.cwd(), 'temp', 'video-analysis');

// 确保临时目录存在
async function ensureTempDir() {
  try {
    await fs.access(TEMP_DIR);
  } catch {
    await fs.mkdir(TEMP_DIR, { recursive: true });
  }
}

// 清理临时文件
async function cleanupTempFiles(sessionId: string) {
  try {
    const sessionDir = path.join(TEMP_DIR, sessionId);
    await fs.rm(sessionDir, { recursive: true, force: true });
  } catch (error) {
    console.warn('清理临时文件失败:', error);
  }
}

// 验证视频URL
function validateVideoUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);

    // 只支持HTTP和HTTPS协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }

    // 支持的视频平台域名
    const supportedDomains = [
      'youtube.com', 'youtu.be', 'bilibili.com', 'b23.tv',
      'vimeo.com', 'dailymotion.com'
    ];

    // 支持的视频文件扩展名
    const supportedExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.flv', '.m4v'];

    // 检查是否为支持的平台
    const isPlatformSupported = supportedDomains.some(domain =>
      urlObj.hostname.includes(domain)
    );

    // 检查是否为媒体服务器URL（优先级高于直链检查）
    const isMediaServerUrl = isMediaServerURL(urlObj);

    // 检查是否为直链视频文件（仅当不是媒体服务器URL时）
    const isDirectVideoLink = !isMediaServerUrl && supportedExtensions.some(ext =>
      urlObj.pathname.toLowerCase().includes(ext)
    );

    return isPlatformSupported || isDirectVideoLink || isMediaServerUrl;
  } catch {
    return false;
  }
}

// 检查是否为媒体服务器URL
function isMediaServerURL(urlObj: URL): boolean {
  const path = urlObj.pathname.toLowerCase();
  const searchParams = urlObj.searchParams;

  // Emby/Jellyfin服务器特征
  if ((path.includes('/emby/') || path.includes('/jellyfin/')) && path.includes('/stream')) {
    return searchParams.has('api_key') || searchParams.has('ApiKey');
  }

  // Plex服务器特征
  if (path.includes('/library/') && path.includes('/parts/')) {
    return searchParams.has('X-Plex-Token');
  }

  // 通用媒体服务器特征
  if (path.includes('stream')) {
    return searchParams.has('api_key') ||
           searchParams.has('ApiKey') ||
           searchParams.has('token') ||
           searchParams.has('X-Plex-Token');
  }

  return false;
}



// 从URL提取视频信息（不下载视频）
async function extractVideoInfoFromUrl(videoUrl: string): Promise<any> {
  try {
    const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${videoUrl}"`;
    const { stdout } = await execAsync(command, { timeout: 30000 }); // 30秒超时
    return JSON.parse(stdout);
  } catch (error) {
    console.error('提取视频信息失败:', error);
    // 返回默认信息
    return {
      format: {
        duration: 0,
        tags: {
          title: '未知标题'
        }
      }
    };
  }
}

// 从URL提取字幕（不下载视频）
async function extractSubtitlesFromUrl(videoUrl: string, sessionId: string): Promise<SubtitleExtractionResult> {
  const sessionDir = path.join(TEMP_DIR, sessionId);
  await fs.mkdir(sessionDir, { recursive: true });

  try {
    // 尝试提取内嵌字幕
    const outputPath = path.join(sessionDir, 'subtitles.srt');
    const command = `ffmpeg -i "${videoUrl}" -map 0:s:0 -c:s srt "${outputPath}"`;

    try {
      await execAsync(command, { timeout: 60000 }); // 1分钟超时
      const subtitleContent = await fs.readFile(outputPath, 'utf-8');

      // 解析字幕内容
      const subtitleExtractor = new SubtitleExtractor(sessionDir);
      const segments = subtitleExtractor.parseSRTSubtitles(subtitleContent);

      return {
        tracks: [{
          index: 0,
          language: 'unknown',
          codec: 'srt',
          type: 'embedded'
        }],
        segments,
        totalDuration: segments.length > 0 ? segments[segments.length - 1].end : 0,
        extractedContent: segments.map(s => s.text).join(' '),
        language: 'unknown',
        confidence: 0.8
      };
    } catch (error) {
      console.warn('未找到内嵌字幕:', error);
    }

    // 如果没有内嵌字幕，返回空结果
    return {
      tracks: [],
      segments: [],
      totalDuration: 0,
      extractedContent: '',
      language: 'unknown',
      confidence: 0
    };

  } catch (error) {
    console.error('字幕提取失败:', error);
    return {
      tracks: [],
      segments: [],
      totalDuration: 0,
      extractedContent: '',
      language: 'unknown',
      confidence: 0
    };
  } finally {
    // 清理临时文件
    try {
      await cleanupTempFiles(sessionId);
    } catch (error) {
      console.warn('清理临时文件失败:', error);
    }
  }
}



// 流式提取音频并转文字（不下载完整视频）
async function transcribeVideoAudio(videoUrl: string, apiKey: string, asrModel: string = 'FunAudioLLM/SenseVoiceSmall'): Promise<{
  transcript: string;
  segments: Array<{
    start: number;
    end: number;
    text: string;
    confidence: number;
  }>;
}> {
  const sessionId = uuidv4();
  const sessionDir = path.join(TEMP_DIR, sessionId);
  await fs.mkdir(sessionDir, { recursive: true });

  try {
    // 使用ffmpeg流式提取音频，分段处理
    const segments = await extractAudioSegments(videoUrl, sessionDir);

    const allSegments: Array<{
      start: number;
      end: number;
      text: string;
      confidence: number;
    }> = [];

    let fullTranscript = '';

    // 处理每个音频段
    for (const segment of segments) {
      try {
        const segmentResult = await transcribeAudioSegment(segment.path, apiKey, asrModel);

        if (segmentResult.text.trim()) {
          allSegments.push({
            start: segment.startTime,
            end: segment.endTime,
            text: segmentResult.text.trim(),
            confidence: segmentResult.confidence
          });

          fullTranscript += segmentResult.text.trim() + ' ';
        }
      } catch (error) {
        console.warn(`处理音频段 ${segment.startTime}-${segment.endTime} 失败:`, error);
      }
    }

    // 清理临时文件
    await cleanupTempFiles(sessionId);

    return {
      transcript: fullTranscript.trim(),
      segments: allSegments
    };

  } catch (error) {
    console.error('音频转文字失败:', error);
    await cleanupTempFiles(sessionId);

    return {
      transcript: '音频转文字失败，请检查视频URL和API配置',
      segments: []
    };
  }
}

// 提取音频段（30秒一段）
async function extractAudioSegments(videoUrl: string, sessionDir: string): Promise<Array<{
  path: string;
  startTime: number;
  endTime: number;
}>> {
  try {
    // 首先获取视频时长
    const durationCommand = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${videoUrl}"`;
    const { stdout: durationOutput } = await execAsync(durationCommand);
    const totalDuration = parseFloat(durationOutput.trim());

    if (!totalDuration || totalDuration <= 0) {
      throw new Error('无法获取视频时长');
    }

    const segmentDuration = 30; // 30秒一段
    const segments: Array<{ path: string; startTime: number; endTime: number }> = [];

    // 分段提取音频
    for (let start = 0; start < totalDuration; start += segmentDuration) {
      const end = Math.min(start + segmentDuration, totalDuration);
      const segmentPath = path.join(sessionDir, `segment_${start}_${end}.wav`);

      // 使用ffmpeg提取音频段
      const command = `ffmpeg -ss ${start} -i "${videoUrl}" -t ${end - start} -vn -acodec pcm_s16le -ar 16000 -ac 1 "${segmentPath}"`;

      try {
        await execAsync(command, { timeout: 60000 }); // 1分钟超时
        segments.push({
          path: segmentPath,
          startTime: start,
          endTime: end
        });
      } catch (error) {
        console.warn(`提取音频段 ${start}-${end} 失败:`, error);
      }
    }

    return segments;
  } catch (error) {
    console.error('提取音频段失败:', error);
    throw error;
  }
}

// 转录单个音频段（只使用硅基流动模型）
async function transcribeAudioSegment(audioPath: string, apiKey: string, asrModel: string): Promise<{
  text: string;
  confidence: number;
}> {
  try {
    // 只使用硅基流动的ASR模型
    return await transcribeWithSiliconFlow(audioPath, apiKey, asrModel);
  } catch (error) {
    console.error('音频段转录失败:', error);
    return {
      text: '',
      confidence: 0
    };
  }
}

// 使用硅基流动ASR模型转录
async function transcribeWithSiliconFlow(audioPath: string, apiKey: string, model: string): Promise<{
  text: string;
  confidence: number;
}> {
  try {
    console.log(`使用硅基流动模型 ${model} 进行音频转文字`);

    // 读取音频文件并转换为base64
    const audioBuffer = await fs.readFile(audioPath);
    const base64Audio = audioBuffer.toString('base64');

    // 根据模型选择合适的API端点和参数
    let apiEndpoint = 'https://api.siliconflow.cn/v1/audio/transcriptions';
    let requestBody: any;

    if (model.includes('SenseVoice')) {
      // SenseVoice模型的API调用
      requestBody = {
        model: model,
        audio: base64Audio,
        language: 'zh', // 中文
        response_format: 'json'
      };
    } else if (model.includes('paraformer')) {
      // Paraformer模型的API调用
      requestBody = {
        model: model,
        audio: base64Audio,
        language: model.includes('zh') ? 'zh' : 'en',
        response_format: 'json'
      };
    } else {
      // 默认配置
      requestBody = {
        model: model,
        audio: base64Audio,
        language: 'auto',
        response_format: 'json'
      };
    }

    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`硅基流动ASR API调用失败: ${response.status} - ${errorText}`);

      // 如果API调用失败，返回模拟结果
      return {
        text: `[${model}模型] 音频转文字功能调用失败，请检查API密钥和模型配置。错误: ${response.status}`,
        confidence: 0.3
      };
    }

    const result = await response.json();

    // 解析硅基流动的响应格式
    let transcriptText = '';
    let confidence = 0.8;

    if (result.text) {
      transcriptText = result.text;
    } else if (result.transcription) {
      transcriptText = result.transcription;
    } else if (result.result) {
      transcriptText = result.result;
    } else {
      console.warn('未知的响应格式:', result);
      transcriptText = JSON.stringify(result);
    }

    // 提取置信度
    if (result.confidence) {
      confidence = result.confidence;
    } else if (result.score) {
      confidence = result.score;
    }

    return {
      text: transcriptText.trim() || '',
      confidence: confidence
    };

  } catch (error) {
    console.error('硅基流动转录失败:', error);

    // 返回错误信息而不是抛出异常
    return {
      text: `[${model}模型] 音频转文字处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
      confidence: 0.1
    };
  }
}



// 生成结构化的视频内容描述（用于后续简介生成）
async function generateStructuredContent(
  audioTranscript: string,
  subtitleResult: SubtitleExtractionResult,
  videoInfo: any
): Promise<string> {
  // 构建结构化的内容描述，基于音频和字幕内容
  const content = [
    '=== AI音频分析结果 ===',
    '',
    `【视频信息】`,
    `标题: ${videoInfo.title || '未知标题'}`,
    `时长: ${Math.floor(videoInfo.duration / 60)}分${Math.floor(videoInfo.duration % 60)}秒`,
    '',
    '【音频转文字内容】',
    audioTranscript || '暂无音频内容',
    '',
    '【字幕内容】',
    subtitleResult.hasSubtitles ?
      `语言: ${subtitleResult.language}, 置信度: ${(subtitleResult.confidence * 100).toFixed(1)}%` :
      '未检测到字幕',
    subtitleResult.extractedContent ?
      subtitleResult.extractedContent.substring(0, 1000) + (subtitleResult.extractedContent.length > 1000 ? '...' : '') :
      '无字幕内容',
    '',
    '【内容分析】',
    subtitleResult.hasSubtitles && subtitleResult.extractedContent ?
      '主要基于字幕内容进行分析，字幕提供了准确的对话和情节信息。' :
      '主要基于音频转文字内容进行分析，通过语音识别获取对话和情节信息。',
    '',
    '【生成建议】',
    '建议优先使用字幕内容（如有），结合音频转文字结果，生成准确的分集简介。',
    ''
  ].join('\n');

  return content;
}

// 生成综合总结
async function generateCombinedSummary(
  audioTranscript: string,
  subtitleResult: SubtitleExtractionResult,
  apiKey: string
): Promise<string> {
  try {
    const prompt = `基于以下音频和字幕分析结果，生成一个简洁的分集简介：

音频转文字内容：
${audioTranscript}

字幕内容：
${subtitleResult.hasSubtitles ?
  `语言: ${subtitleResult.language}\n内容: ${subtitleResult.extractedContent.substring(0, 500)}${subtitleResult.extractedContent.length > 500 ? '...' : ''}` :
  '未检测到字幕内容'}

请生成一个120-200字的分集简介，要求：
1. 优先使用字幕内容作为主要信息源（如果有的话）
2. 如果没有字幕或字幕质量不佳，则使用音频转文字内容
3. 整合音频和字幕信息，突出主要情节和看点
4. 语言生动有趣，符合影视剧简介风格
5. 避免剧透关键结局
6. 重点体现对话和情节发展
7. 确保内容准确性，避免添加不存在的情节元素`;

    const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-ai/DeepSeek-V2.5',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.7,
        max_tokens: 300
      })
    });

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '生成简介失败';
  } catch (error) {
    console.error('生成综合总结失败:', error);
    return '生成综合总结失败';
  }
}

export async function POST(request: NextRequest) {
  const sessionId = uuidv4();

  try {
    await ensureTempDir();

    const body = await request.json();
    const { videoUrl, apiKey, asrModel = 'FunAudioLLM/SenseVoiceSmall' } = body;

    if (!videoUrl) {
      return NextResponse.json({
        success: false,
        error: '请提供视频URL'
      }, { status: 400 });
    }

    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: '请提供硅基流动API密钥'
      }, { status: 400 });
    }

    if (!validateVideoUrl(videoUrl)) {
      return NextResponse.json({
        success: false,
        error: '不支持的视频URL格式'
      }, { status: 400 });
    }

    // 1. 获取视频基本信息（不下载视频）
    console.log('获取视频信息...');
    const videoInfo = await extractVideoInfoFromUrl(videoUrl);
    const duration = parseFloat(videoInfo.format.duration) || 0;

    // 2. 流式提取音频并转文字
    console.log('开始音频转文字处理...');
    const audioResult = await transcribeVideoAudio(videoUrl, apiKey, asrModel);

    // 3. 提取字幕（如果可能）
    console.log('尝试提取字幕...');
    const subtitleResult = await extractSubtitlesFromUrl(videoUrl, sessionId);

    // 4. 生成综合总结和结构化内容
    console.log('生成综合总结...');
    const [combinedSummary, structuredContent] = await Promise.all([
      generateCombinedSummary(audioResult.transcript, subtitleResult, apiKey),
      generateStructuredContent(audioResult.transcript, subtitleResult, {
        title: videoInfo.format.tags?.title || '未知标题',
        duration
      })
    ]);

    // 5. 构建返回结果
    const result: VideoAnalysisResult = {
      success: true,
      data: {
        videoInfo: {
          title: videoInfo.format.tags?.title || '未知标题',
          duration,
          description: videoInfo.format.tags?.description
        },
        audioAnalysis: {
          transcript: audioResult.transcript,
          segments: audioResult.segments, // 带时间戳的分段转录
          summary: audioResult.transcript.length > 200 ?
            audioResult.transcript.substring(0, 200) + '...' :
            audioResult.transcript
        },
        subtitleAnalysis: {
          hasSubtitles: subtitleResult.segments.length > 0,
          language: subtitleResult.language,
          content: subtitleResult.extractedContent,
          segments: subtitleResult.segments,
          confidence: subtitleResult.confidence
        },
        combinedSummary,
        structuredContent // 添加结构化内容，用于简介生成
      }
    };
    
    // 6. 清理临时文件
    setTimeout(() => {
      cleanupTempFiles(sessionId);
    }, 60000); // 1分钟后清理
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('视频分析失败:', error);
    
    // 清理临时文件
    await cleanupTempFiles(sessionId);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '视频分析失败'
    }, { status: 500 });
  }
}
